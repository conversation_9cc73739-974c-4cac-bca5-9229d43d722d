package client;

import org.apache.http.impl.client.CloseableHttpClient;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import util.Utils;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.net.*;
import java.nio.charset.StandardCharsets;

public class UDPTestClient implements ITestClient {

    private static final Logger logger = LoggerFactory.getLogger(UDPTestClient.class);

    private final String serverIP;
    private final int udpPort;

    private boolean deleteAfterDownload;
    private File localFile;
    private byte[] fileData;
    private String localChecksum;

    private DatagramSocket socket;
    private InetAddress serverAddress;


    /**
     * Creates a new UDP test runner.
     *
     * @param serverIP The server IP address
     * @param udpPort The UDP port to connect to
     */
    public UDPTestClient(String serverIP, int udpPort) {
        this.serverIP = serverIP;
        this.udpPort = udpPort;
    }

    @Override
    public String getTestType() {
        return "UDP";
    }

    @Override
    public void setupTest(File localFile, boolean deleteAfterDownload) throws IOException {
        this.deleteAfterDownload = deleteAfterDownload;
        this.localFile = localFile;

        this.fileData = Utils.readFile(localFile);
        this.localChecksum = Utils.computeSHA256(fileData);

        this.socket = new DatagramSocket();
        socket.setSoTimeout(15000);

        this.serverAddress = InetAddress.getByName(serverIP);
    }

    @Override
    public boolean executeTestIteration(int iterationNumber) {
        try {
            logger.info("Uploading '{}' ({} bytes), SHA-256: {}...",
                       localFile.getAbsolutePath(), localFile.length(), localChecksum);

            DatagramPacket uploadPacket = new DatagramPacket(fileData, fileData.length,
                                                            serverAddress, udpPort);
            socket.send(uploadPacket);

            byte[] respBuf = new byte[1024];
            DatagramPacket respPacket = new DatagramPacket(respBuf, respBuf.length);
            socket.receive(respPacket);
            String uploadResponse = new String(respPacket.getData(), 0, respPacket.getLength(), StandardCharsets.UTF_8);
            logger.info("Upload response: {}", uploadResponse);

            String[] parts = uploadResponse.split("\\|");
            if (parts.length < 3 || !parts[0].equals("200")) {
                logger.error("Upload failed: {}", uploadResponse);
                return false;
            }
            String fileNameOnServer = parts[2].trim();

            logger.info("Upload successful, file name on server: {}", fileNameOnServer);

            logger.info("Downloading file {}...", fileNameOnServer);
            String query = "file=" + fileNameOnServer + "&delete=" + (deleteAfterDownload ? "true" : "false");
            byte[] downloadRequest = query.getBytes(StandardCharsets.UTF_8);
            DatagramPacket downloadPacket = new DatagramPacket(downloadRequest, downloadRequest.length,
                                                              serverAddress, udpPort);
            socket.send(downloadPacket);

            byte[] downloadBuf = new byte[65535];
            DatagramPacket downloadResp = new DatagramPacket(downloadBuf, downloadBuf.length);
            socket.receive(downloadResp);

            // Warn if source IP of the response is different from the IP the original request was sent to.
            // While for many use cases this will not be an issue, some DNS clients will reject such responses
            // in which case a DNS resolution will fail.
            InetAddress responseSourceIp = downloadResp.getAddress();
            if (!responseSourceIp.equals(serverAddress)) {
                logger.warn("Source IP of UDP response packet (" + responseSourceIp.getHostAddress() + ") is " +
                        "different from the IP the UDP request was sent to (" + serverIP + "). This might cause " +
                        "issues eg. with UDP-based DNS requests!");
            } else {
                logger.info("Successfully verified that the source IP of the UDP response packet (" +
                        responseSourceIp.getHostAddress() + ") is identical to the IP the UDP request was sent to (" +
                        serverIP + ")");
            }

            File downloadedFile = new File("downloaded_udp_" + fileNameOnServer);
            FileOutputStream fos = new FileOutputStream(downloadedFile);
            fos.write(downloadResp.getData(), downloadResp.getOffset(), downloadResp.getLength());
            fos.close();

            String downloadedChecksum = Utils.computeSHA256(downloadedFile);

            logger.info("Downloaded file saved as '{}' ({} bytes), SHA-256: {}",
                    downloadedFile.getAbsolutePath(), downloadedFile.length(), downloadedChecksum);

            if (localChecksum.equals(downloadedChecksum)) {
                logger.info("SUCCESS: Checksum matches.");
                if (deleteAfterDownload && downloadedFile.delete()) {
                    logger.info("Downloaded file deleted from client side as requested.");
                } else if (deleteAfterDownload) {
                    logger.error("Could not delete downloaded file from client side.");
                }
                return true;
            } else {
                logger.error("Checksum mismatch! File kept for troubleshooting.");
                return false;
            }
        } catch (SocketTimeoutException ste) {
            logger.error("UDP receive timeout after 15 seconds.");
            return false;
        } catch (Exception e) {
            logger.error("Exception during UDP iteration: {}", e.getMessage(), e);
            return false;
        }
    }

    @Override
    public void cleanupTest() {
        if (socket != null) {
            socket.close();
        }
    }
}
